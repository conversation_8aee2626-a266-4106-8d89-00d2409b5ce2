import { jsxs as _jsxs, jsx as _jsx } from "react/jsx-runtime";
import React from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';
export default function ProfilesPagination({ currentPage, totalItems, itemsPerPage, onPageChange, from, to, }) {
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    // Calcular from e to se não fornecidos pela API
    const displayFrom = from ?? ((currentPage - 1) * itemsPerPage + 1);
    const displayTo = to ?? Math.min(currentPage * itemsPerPage, totalItems);
    // Gerar array de páginas para mostrar
    const getPageNumbers = () => {
        const pages = [];
        const maxVisiblePages = 5;
        if (totalPages <= maxVisiblePages) {
            // Se há poucas páginas, mostrar todas
            for (let i = 1; i <= totalPages; i++) {
                pages.push(i);
            }
        }
        else {
            // Lógica para mostrar páginas com ellipsis
            if (currentPage <= 3) {
                // Início: 1, 2, 3, 4, ..., last
                for (let i = 1; i <= 4; i++) {
                    pages.push(i);
                }
                if (totalPages > 5) {
                    pages.push('...');
                    pages.push(totalPages);
                }
            }
            else if (currentPage >= totalPages - 2) {
                // Fim: 1, ..., last-3, last-2, last-1, last
                pages.push(1);
                if (totalPages > 5) {
                    pages.push('...');
                }
                for (let i = totalPages - 3; i <= totalPages; i++) {
                    pages.push(i);
                }
            }
            else {
                // Meio: 1, ..., current-1, current, current+1, ..., last
                pages.push(1);
                pages.push('...');
                for (let i = currentPage - 1; i <= currentPage + 1; i++) {
                    pages.push(i);
                }
                pages.push('...');
                pages.push(totalPages);
            }
        }
        return pages;
    };
    const pageNumbers = getPageNumbers();
    const handlePrevious = () => {
        if (currentPage > 1) {
            onPageChange(currentPage - 1);
        }
    };
    const handleNext = () => {
        if (currentPage < totalPages) {
            onPageChange(currentPage + 1);
        }
    };
    const handlePageClick = (page) => {
        if (typeof page === 'number') {
            onPageChange(page);
        }
    };
    if (totalItems === 0) {
        return null;
    }
    return (_jsxs("div", { className: "flex items-center justify-between p-4", children: [_jsxs("div", { className: "text-sm text-gray-600 dark:text-gray-400", children: ["Mostrando de ", displayFrom, " a ", displayTo, " de ", totalItems, " perfis"] }), _jsxs("div", { className: "flex items-center gap-2", children: [_jsx(Button, { variant: "outline", size: "sm", onClick: handlePrevious, disabled: currentPage === 1, className: "w-8 h-8 p-0 bg-white dark:bg-neutral-900 text-black dark:text-white border-gray-300 dark:border-neutral-600 hover:bg-gray-100 dark:hover:bg-neutral-800 disabled:opacity-50 disabled:cursor-not-allowed", children: _jsx(ChevronLeft, { className: "w-4 h-4" }) }), pageNumbers.map((page, index) => (_jsx(React.Fragment, { children: page === '...' ? (_jsx("span", { className: "px-2 text-gray-500", children: "..." })) : (_jsx(Button, { variant: "outline", size: "sm", onClick: () => handlePageClick(page), className: `w-8 h-8 p-0 ${page === currentPage
                                ? 'bg-primary-500 text-black border-primary-500 hover:bg-primary-600'
                                : 'bg-white dark:bg-neutral-900 text-black dark:text-white border-gray-300 dark:border-neutral-600 hover:bg-gray-100 dark:hover:bg-neutral-800'}`, children: page })) }, index))), _jsx(Button, { variant: "outline", size: "sm", onClick: handleNext, disabled: currentPage === totalPages, className: "w-8 h-8 p-0 bg-white dark:bg-neutral-900 text-black dark:text-white border-gray-300 dark:border-neutral-600 hover:bg-gray-100 dark:hover:bg-neutral-800 disabled:opacity-50 disabled:cursor-not-allowed", children: _jsx(ChevronRight, { className: "w-4 h-4" }) })] })] }));
}
