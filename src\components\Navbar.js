import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { ThemeToggle } from './theme-toggle';
const Navbar = () => (_jsx("div", { className: "bg-neutral-950 border-b border-neutral-800 mb-8", children: _jsxs("nav", { className: "flex items-center justify-between gap-2 px-4 ", children: [_jsx("img", { src: "/logo.svg", alt: "Hype Logo", className: "h-20 w-auto" }), _jsx(ThemeToggle, {})] }) }));
export default Navbar;
