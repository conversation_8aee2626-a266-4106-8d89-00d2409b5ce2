const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;
const CAMPANHA_ID = import.meta.env.VITE_CAMPANHA_ID;
//parametro hash
export async function fetchCampaignData() {
    const res = await fetch(`${API_BASE_URL}/campaigns/${CAMPANHA_ID}/report`);
    if (!res.ok)
        throw new Error('Erro ao buscar dados da campanha');
    return res.json();
}
//parametro hash
//search to filter
export async function fetchInfluencersData(page = 1, perPage = 10, search = '', network = null, orderBy = 'views') {
    const searchParam = search ? `&search=${encodeURIComponent(search)}` : '';
    // Não envie o parâmetro network quando for null
    const networkParam = network !== null ? `&network=${encodeURIComponent(network.toString())}` : '';
    const orderParam = orderBy ? `&order_by=${encodeURIComponent(orderBy)}` : '';
    const res = await fetch(`${API_BASE_URL}/campaigns/${CAMPANHA_ID}/report/social-networks?page=${page}&per_page=${perPage}${searchParam}${networkParam}${orderParam}`);
    if (!res.ok)
        throw new Error('Erro ao buscar dados dos influencers');
    return res.json();
}
