// src/app/router.tsx
import { createRootRoute, createRoute, createRouter, } from '@tanstack/react-router';
import App from '../App';
import AnalyticsDashboard from '../pages/campaign-report/analytics-dashboard';
// Root Route (ponto de entrada para todas as rotas)
export const rootRoute = createRootRoute({
    component: App,
});
// Subrotas
const campaignReportRoute = createRoute({
    path: '/',
    getParentRoute: () => rootRoute,
    component: AnalyticsDashboard,
});
// Roteador
export const router = createRouter({
    routeTree: rootRoute.addChildren([campaignReportRoute]),
});
