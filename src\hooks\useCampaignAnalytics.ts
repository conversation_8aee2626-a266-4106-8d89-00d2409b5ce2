import { useState, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import debounce from 'lodash/debounce';
import { fetchCampaignData, fetchInfluencersData } from '@/APIs/analytics';
import { CampaignData, InfluencersData } from '@/types/api';
import { SOCIAL_NETWORK_TYPES, ORDER_BY_OPTIONS } from '@/constants/socialNetworks';

export interface UseCampaignAnalyticsReturn {
  // Data
  campaignData: CampaignData | undefined;
  influencersData: InfluencersData | undefined;
  socialPlatforms: Array<{
    name: string;
    icon: React.ReactNode;
    bgColor: string;
    stats: {
      women: string;
      men: string;
      engagement: {
        likes: number;
        comments: number;
        shares: number;
      };
    };
  }>;
  influencers: Array<{
    name: string;
    platform: string;
    platformType: number;
    verified: boolean;
    views: number;
    likes: number;
    comments: number;
    womenPercent: string;
    menPercent: string;
    photo: string;
    postLink: string;
  }>;
  
  // Computed values
  totalViews: number;
  totalLikes: number;
  totalComments: number;
  totalGenders: { female: number; male: number };
  profilesCount: number | string;
  title: string;
  
  // State
  currentPage: number;
  itemsPerPage: number;
  search: string;
  debouncedSearch: string;
  selectedNetwork: number | null;
  orderBy: string;
  
  // Actions
  setCurrentPage: (page: number) => void;
  setSearch: (search: string) => void;
  setSelectedNetwork: (network: number | null) => void;
  setOrderBy: (orderBy: string) => void;
  handleSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handlePageChange: (page: number) => void;
}

export function useCampaignAnalytics(): UseCampaignAnalyticsReturn {
  // Estados para paginação e filtros
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [search, setSearch] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const [selectedNetwork, setSelectedNetwork] = useState<number | null>(SOCIAL_NETWORK_TYPES.ALL);
  const [orderBy, setOrderBy] = useState<string>(ORDER_BY_OPTIONS.VIEWS);

  // Fetch dos dados gerais da campanha
  const { data: campaignData } = useQuery<CampaignData>({
    queryKey: ['campaign-report'],
    queryFn: fetchCampaignData,
  });

  // Fetch dos dados por perfil (influencers) com parâmetro de busca
  const { data: influencersData } = useQuery<InfluencersData>({
    queryKey: ['campaign-report-influencers', currentPage, itemsPerPage, debouncedSearch, selectedNetwork, orderBy],
    queryFn: () => fetchInfluencersData(currentPage, itemsPerPage, debouncedSearch, selectedNetwork, orderBy),
  });

  // Debounce function for search
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSetSearch = useCallback(
    debounce((value: string) => {
      setDebouncedSearch(value);
    }, 1000),
    []
  );

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearch(value);
    debouncedSetSearch(value);
  };

  // Função para lidar com mudança de página
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Computed values
  const totalViews = campaignData?.views_count || 0;
  const totalLikes = campaignData?.likes_count || 0;
  const totalComments = campaignData?.comments_count || 0;
  const totalGenders = campaignData?.genders || { female: 0, male: 0 };
  const profilesCount = typeof influencersData?.total === 'number' ? influencersData.total : 'não informado';
  const title = campaignData?.name || 'TITULO DE CAMPANHA NÃO INFORMADO';

  return {
    // Data
    campaignData,
    influencersData,
    socialPlatforms: [], // Will be computed in component with SOCIAL_NETWORK_CONFIG
    influencers: [], // Will be computed in component with SOCIAL_NETWORK_CONFIG
    
    // Computed values
    totalViews,
    totalLikes,
    totalComments,
    totalGenders,
    profilesCount,
    title,
    
    // State
    currentPage,
    itemsPerPage,
    search,
    debouncedSearch,
    selectedNetwork,
    orderBy,
    
    // Actions
    setCurrentPage,
    setSearch,
    setSelectedNetwork,
    setOrderBy,
    handleSearchChange,
    handlePageChange,
  };
}
