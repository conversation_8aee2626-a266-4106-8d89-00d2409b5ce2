import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { Search, Eye, Heart, MessageCircle, Venus, Mars, } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, } from '@/components/ui/select';
import CampaignHeader from '@/components/CampaignHeader';
import Navbar from '@/components/Navbar';
import ProfilesPagination from '@/components/ProfilesPagination';
import { useCampaignAnalytics } from '@/hooks/useCampaignAnalytics';
import { SOCIAL_NETWORK_TYPES, ORDER_BY_OPTIONS, SOCIAL_NETWORK_CONFIG, getPlatformConfig } from '@/constants/socialNetworks';
export default function AnalyticsDashboard() {
    // Use custom hook for campaign analytics
    const { campaignData, influencersData, totalViews, totalLikes, totalComments, totalGenders, profilesCount, title, itemsPerPage, search, selectedNetwork, orderBy, handleSearchChange, handlePageChange, setSelectedNetwork, setOrderBy, } = useCampaignAnalytics();
    // Social platforms breakdown
    const socialPlatforms = campaignData?.by_social_network?.map((platform) => ({
        name: SOCIAL_NETWORK_CONFIG[platform.type]?.name || platform.name,
        icon: SOCIAL_NETWORK_CONFIG[platform.type]?.icon,
        bgColor: SOCIAL_NETWORK_CONFIG[platform.type]?.bgColor,
        stats: {
            women: `${platform.genders.female}% mulheres`,
            men: `${platform.genders.male}% homens`,
            engagement: {
                likes: platform.likes_count,
                comments: platform.comments_count,
                shares: platform.views_count, // Não há shares, usar views_count como exemplo
            },
        },
    })) || [];
    // Influencers table
    const influencers = influencersData?.data?.map((item) => ({
        name: item.influencer_name,
        platform: SOCIAL_NETWORK_CONFIG[item.social_network_type]?.key || 'desconhecido',
        platformType: item.social_network_type,
        verified: false, // Não há campo de verificado
        views: item.views_count,
        likes: item.likes_count,
        comments: item.comments_count,
        womenPercent: `${item.genders.female}%`,
        menPercent: `${item.genders.male}%`,
        photo: item.influencer_photo,
        postLink: item.post_link,
    })) || [];
    return (_jsx(_Fragment, { children: _jsxs("div", { className: "min-h-screen bg-neutral-950 text-white p-6", children: [_jsx(Navbar, {}), _jsx(CampaignHeader, { title: title, profilesCount: profilesCount, startDate: campaignData?.start_date, endDate: campaignData?.end_date }), _jsxs("div", { className: "mb-8", children: [_jsx("h2", { className: "text-lg font-semibold mb-4", children: "Geral" }), _jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4", children: [_jsx(Card, { className: "bg-white dark:bg-neutral-900 text-black dark:text-white", children: _jsxs(CardContent, { className: "p-4", children: [_jsxs("div", { className: "flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2", children: [_jsx(Eye, { className: "w-4 h-4" }), _jsx("span", { children: "Visualiza\u00E7\u00F5es" })] }), _jsx("div", { className: "text-2xl font-medium", children: totalViews })] }) }), _jsx(Card, { className: "bg-white dark:bg-neutral-900 text-black dark:text-white", children: _jsxs(CardContent, { className: "p-4", children: [_jsxs("div", { className: "flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2", children: [_jsx(Heart, { className: "w-4 h-4" }), _jsx("span", { children: "Curtidas" })] }), _jsx("div", { className: "text-2xl font-medium", children: totalLikes })] }) }), _jsx(Card, { className: "bg-white dark:bg-neutral-900 text-black dark:text-white", children: _jsxs(CardContent, { className: "p-4", children: [_jsxs("div", { className: "flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2", children: [_jsx(MessageCircle, { className: "w-4 h-4" }), _jsx("span", { children: "Coment\u00E1rios" })] }), _jsx("div", { className: "text-2xl font-medium", children: totalComments })] }) }), _jsx(Card, { className: "bg-white dark:bg-neutral-900 text-black dark:text-white", children: _jsxs(CardContent, { className: "p-4", children: [_jsxs("div", { className: "flex items-center gap-2 text-sm text-gray-600 mb-2", children: [_jsx(Venus, { className: "w-4 h-4 text-pink-400" }), _jsx(Mars, { className: "w-4 h-4 text-blue-400" }), _jsx("span", { children: "G\u00EAnero" })] }), _jsxs("div", { className: "flex items-center gap-4", children: [_jsxs("div", { className: "w-16 h-16 rounded-full bg-gradient-to-r from-pink-400 to-blue-400 flex items-center justify-center relative", children: [_jsx("div", { className: "w-12 h-12 bg-white rounded-full" }), _jsx("div", { className: "absolute inset-0 rounded-full", style: {
                                                                    background: `conic-gradient(#ec4899 0deg ${69.5 * 3.6}deg, #3b82f6 ${69.5 * 3.6}deg 360deg)`,
                                                                } })] }), _jsxs("div", { className: "text-xs", children: [_jsxs("div", { className: "flex items-center gap-1 mb-1", children: [_jsx("div", { className: "w-2 h-2 bg-pink-400 rounded-full" }), _jsxs("span", { children: [totalGenders.female, "% mulheres"] })] }), _jsxs("div", { className: "flex items-center gap-1", children: [_jsx("div", { className: "w-2 h-2 bg-blue-400 rounded-full" }), _jsxs("span", { children: [totalGenders.male, "% homens"] })] })] })] })] }) })] })] }), _jsxs("div", { className: "mb-8", children: [_jsx("h2", { className: "text-lg font-semibold mb-4", children: "Por Rede Social" }), _jsx("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-4", children: socialPlatforms.map((platform, index) => (_jsx(Card, { className: "bg-white dark:bg-neutral-900 text-black dark:text-white", children: _jsxs(CardContent, { className: "p-4", children: [_jsx("div", { className: `w-8 h-8 ${platform.bgColor} rounded-lg flex items-center justify-center text-white mb-3`, children: platform.icon }), _jsxs("div", { className: "mb-3", children: [_jsxs("div", { className: "flex items-center gap-1 mb-1 text-sm", children: [_jsx(Venus, { className: "w-3 h-3 text-pink-400" }), _jsx("span", { children: platform.stats.women })] }), _jsxs("div", { className: "flex items-center gap-1 text-sm", children: [_jsx(Mars, { className: "w-3 h-3 text-blue-400" }), _jsx("span", { children: platform.stats.men })] })] }), _jsxs("div", { className: "flex justify-between text-sm text-gray-600 dark:text-gray-400", children: [_jsxs("span", { children: [_jsx(Heart, { className: "inline w-4 h-4" }), ' ', platform.stats.engagement.likes] }), _jsxs("span", { children: [_jsx(MessageCircle, { className: "inline w-4 h-4" }), ' ', platform.stats.engagement.comments] }), _jsxs("span", { children: [_jsx(Eye, { className: "inline w-4 h-4" }), ' ', platform.stats.engagement.shares] })] })] }) }, index))) })] }), _jsxs("div", { className: "text-white", children: [_jsx("h2", { className: "text-lg font-semibold mb-4", children: "Por Perfil" }), _jsxs("div", { className: "bg-white dark:bg-neutral-900 rounded-lg p-4", children: [_jsxs("div", { className: "flex flex-col md:flex-row gap-4 mb-4", children: [_jsxs("div", { className: "relative flex-1", children: [_jsx(Search, { className: "absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" }), _jsx(Input, { placeholder: "Buscar influencer", className: "pl-10 xl:w-1/3 lg:1/3 bg-neutral-100 rounded-xl dark:bg-neutral-900 text-black dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 border-neutral-300 dark:border-neutral-600", value: search, onChange: handleSearchChange })] }), _jsxs("div", { className: "flex xl:w-2/4 gap-5", children: [_jsxs(Select, { value: selectedNetwork === null ? "null" : selectedNetwork.toString(), onValueChange: (value) => setSelectedNetwork(value === "null" ? null : parseInt(value)), children: [_jsx(SelectTrigger, { className: "text-black dark:text-white bg-neutral-100 rounded-xl dark:bg-neutral-900 border-neutral-300 dark:border-neutral-600", children: _jsx(SelectValue, { placeholder: "Selecionar rede social" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "null", children: "Todas as redes" }), _jsx(SelectItem, { value: SOCIAL_NETWORK_TYPES.INSTAGRAM.toString(), children: "Instagram" }), _jsx(SelectItem, { value: SOCIAL_NETWORK_TYPES.FACEBOOK_PAGE.toString(), children: "Facebook Page" }), _jsx(SelectItem, { value: SOCIAL_NETWORK_TYPES.FACEBOOK_GROUP.toString(), children: "Facebook Group" }), _jsx(SelectItem, { value: SOCIAL_NETWORK_TYPES.TIKTOK.toString(), children: "TikTok" }), _jsx(SelectItem, { value: SOCIAL_NETWORK_TYPES.YOUTUBE.toString(), children: "YouTube" })] })] }), _jsxs(Select, { value: orderBy, onValueChange: setOrderBy, children: [_jsx(SelectTrigger, { className: "text-black dark:text-white bg-neutral-100 rounded-xl dark:bg-neutral-900 border-neutral-300 dark:border-neutral-600", children: _jsx(SelectValue, { placeholder: "Ordenar por" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: ORDER_BY_OPTIONS.VIEWS, children: "Visualiza\u00E7\u00F5es" }), _jsx(SelectItem, { value: ORDER_BY_OPTIONS.LIKES, children: "Curtidas" }), _jsx(SelectItem, { value: ORDER_BY_OPTIONS.COMMENTS, children: "Coment\u00E1rios" })] })] })] })] }), _jsx(Card, { className: "bg-transparent text-black dark:text-white border-none", children: _jsx(CardContent, { className: "p-0", children: _jsx("div", { className: "overflow-x-auto", children: _jsxs("table", { className: "w-full border-none", children: [_jsx("thead", { children: _jsxs("tr", { className: "text-left", children: [_jsx("th", { className: "p-4 font-medium text-gray-600 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700", children: "INFLUENCER" }), _jsx("th", { className: "p-4 font-medium text-gray-600 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700", children: "REDE SOCIAL" }), _jsx("th", { className: "p-4 font-medium text-gray-600 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700", children: "LINK DA PUBLICA\u00C7\u00C3O" }), _jsx("th", { className: "p-4 font-medium text-gray-600 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700", children: "VISUALIZA\u00C7\u00D5ES" }), _jsx("th", { className: "p-4 font-medium text-gray-600 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700", children: "CURTIDAS" }), _jsx("th", { className: "p-4 font-medium text-gray-600 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700", children: "COMENT\u00C1RIOS" }), _jsx("th", { className: "p-4 font-medium text-gray-600 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700", children: "G\u00CANERO" })] }) }), _jsx("tbody", { children: influencers.map((influencer, index) => (_jsxs("tr", { className: "hover:bg-gray-50 dark:hover:bg-neutral-800", children: [_jsx("td", { className: "p-4 border-t border-gray-100 dark:border-gray-700", children: _jsxs("div", { className: "flex items-center gap-2", children: [influencer.photo ? (_jsx("img", { src: influencer.photo, alt: influencer.name, className: "w-8 h-8 rounded-full object-cover bg-gray-200 dark:bg-gray-600" })) : (_jsx("div", { className: "w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full" })), _jsx("span", { className: "font-medium", children: influencer.name })] }) }), _jsx("td", { className: "p-4 border-t border-gray-100 dark:border-gray-700", children: _jsx("div", { className: `w-6 h-6 ${getPlatformConfig(influencer.platform)?.bgColor || 'bg-gray-300 dark:bg-gray-600'} rounded flex items-center justify-center text-white text-xs`, children: getPlatformConfig(influencer.platform)?.icon ||
                                                                            influencer.platform }) }), _jsx("td", { className: "p-4 border-t border-gray-100 dark:border-gray-700", children: _jsx(Button, { variant: "link", className: "p-0 h-auto text-blue-600 dark:text-blue-400", children: "Ver post" }) }), _jsx("td", { className: "p-4 font-medium border-t border-gray-100 dark:border-gray-700", children: influencer.views }), _jsx("td", { className: "p-4 font-medium border-t border-gray-100 dark:border-gray-700", children: influencer.likes }), _jsx("td", { className: "p-4 font-medium border-t border-gray-100 dark:border-gray-700", children: influencer.comments }), _jsx("td", { className: "p-4 border-t border-gray-100 dark:border-gray-700", children: _jsxs("div", { className: "flex items-center gap-2 text-xs", children: [_jsxs("div", { className: "flex items-center gap-1", children: [_jsx("div", { className: "w-6 h-6  bg-pink-400 rounded-xs " }), _jsx("span", { className: "", children: influencer.womenPercent })] }), _jsxs("div", { className: "flex items-center gap-1", children: [_jsx("div", { className: "w-6 h-6 bg-sky-400 rounded-xs" }), _jsx("span", { className: "", children: influencer.menPercent })] })] }) })] }, index))) })] }) }) }) })] }), _jsx(ProfilesPagination, { currentPage: influencersData?.current_page || 1, totalItems: influencersData?.total || 0, itemsPerPage: itemsPerPage, onPageChange: handlePageChange, from: influencersData?.from, to: influencersData?.to })] })] }) }));
}
