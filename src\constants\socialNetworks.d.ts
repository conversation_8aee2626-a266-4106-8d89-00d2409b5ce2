import React from 'react';
export declare const SOCIAL_NETWORK_TYPES: {
    readonly INSTAGRAM: 1;
    readonly FACEBOOK_PAGE: 2;
    readonly FACEBOOK_GROUP: 3;
    readonly TIKTOK: 4;
    readonly YOUTUBE: 5;
    readonly ALL: null;
};
export declare const ORDER_BY_OPTIONS: {
    readonly VIEWS: "views";
    readonly LIKES: "likes";
    readonly COMMENTS: "comments";
};
export declare const SOCIAL_NETWORK_CONFIG: {
    [key: number]: {
        name: string;
        icon: React.ReactNode;
        bgColor: string;
        key: string;
    };
};
export declare const getPlatformConfig: (platformKey: string) => {
    name: string;
    icon: React.ReactNode;
    bgColor: string;
    key: string;
} | undefined;
export declare const getPlatformConfigByType: (platformType: number) => {
    name: string;
    icon: React.ReactNode;
    bgColor: string;
    key: string;
};
