import { Card, CardContent } from '@/components/ui/card';
import React from 'react';
import { Calendar, Users } from 'lucide-react';
import { formatDate } from '@/lib/utils';

interface CampaignHeaderProps {
  title?: string;
  subtitle?: string;
  period?: string;
  startDate?: string;
  endDate?: string;
  profilesCount?: number | string;
}

const CampaignHeader: React.FC<CampaignHeaderProps> = ({
  title = 'Dia dos Namorados (O Boticário)',
  subtitle = 'RELATÓRIO CAMPANHA',
  period,
  startDate,
  endDate,
  profilesCount = 23,
}) => {
  // Format the period using the dates if available
  const formattedPeriod = startDate && endDate 
    ? `${formatDate(startDate)} até ${formatDate(endDate)}`
    : period || '12/05/2025 até 12/06/2025';

  return (
    <div className="mb-8">
      <div className="mb-4">
        <div className="text-sm font-normal text-neutral-400 ml-2 mb-2">{subtitle}</div>
        <h1 className="text-2xl font-normal text-white">{title}</h1>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
        <Card className="bg-white dark:bg-neutral-900 text-black dark:text-white">
          <CardContent className="p-4">
            <div className="text-sm  text-gray-600 dark:text-gray-400 mb-1 flex items-center gap-1">
              <Calendar className="w-4 h-4" />
              Período da duração da campanha
            </div>
            <h3 className="font-medium ">{formattedPeriod}</h3>
          </CardContent>
        </Card>
        <Card className="bg-white dark:bg-neutral-900 text-black dark:text-white">
          <CardContent className="p-4">
            <div className="text-sm text-gray-600 dark:text-gray-400 mb-1 flex items-center gap-1">
              <Users className="w-4 h-4" />
              Quantidade de perfis participantes
            </div>
            <h3 className="font-medium">{profilesCount}</h3>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CampaignHeader; 
