@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
  }

  html {
    font-family: 'Poppins', sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    font-weight: 500; /* Reduzindo de 600/700 para 500 */
  }

  h1 {
    font-size: 1.875rem; /* 30px */
    line-height: 2.25rem; /* 36px */
  }

  h2 {
    font-size: 1.5rem; /* 24px */
    line-height: 2rem; /* 32px */
  }

  h3 {
    font-size: 1.25rem; /* 20px */
    line-height: 1.75rem; /* 28px */
  }

  /* Cores customizadas */
  .bg-primary-500 {
    background-color: #CEFC00 !important;
  }

  .border-primary-500 {
    border-color: #CEFC00 !important;
  }

  .hover\:bg-primary-600:hover {
    background-color: #8DB500 !important;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 11%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 11%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 11%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 11%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 11%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 11%;
    --ring: 0 0% 83.1%;
  }
}

@layer base {
  body {
    background-color: #0a0a0a; /* neutral-950 para ambos os modos */
    color: hsl(var(--foreground));
    transition: background-color 0.3s ease, color 0.3s ease;
  }
}

