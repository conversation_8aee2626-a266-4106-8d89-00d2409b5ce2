import {
  Search,
  Eye,
  Heart,
  MessageCircle,
  Venus,
  Mars,
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import CampaignHeader from '@/components/CampaignHeader';
import Navbar from '@/components/Navbar';
import ProfilesPagination from '@/components/ProfilesPagination';
import { SocialNetwork, InfluencerItem } from '@/types/api';
import { useCampaignAnalytics } from '@/hooks/useCampaignAnalytics';
import {
  SOCIAL_NETWORK_TYPES,
  ORDER_BY_OPTIONS,
  SOCIAL_NETWORK_CONFIG,
  getPlatformConfig
} from '@/constants/socialNetworks';

export default function AnalyticsDashboard() {
  // Use custom hook for campaign analytics
  const {
    campaignData,
    influencersData,
    totalViews,
    totalLikes,
    totalComments,
    totalGenders,
    profilesCount,
    title,
    itemsPerPage,
    search,
    selectedNetwork,
    orderBy,
    handleSearchChange,
    handlePageChange,
    setSelectedNetwork,
    setOrderBy,
  } = useCampaignAnalytics();

  // Social platforms breakdown
  const socialPlatforms =
    campaignData?.by_social_network?.map((platform: SocialNetwork) => ({
      name: SOCIAL_NETWORK_CONFIG[platform.type]?.name || platform.name,
      icon: SOCIAL_NETWORK_CONFIG[platform.type]?.icon,
      bgColor: SOCIAL_NETWORK_CONFIG[platform.type]?.bgColor,
      stats: {
        women: `${platform.genders.female}% mulheres`,
        men: `${platform.genders.male}% homens`,
        engagement: {
          likes: platform.likes_count,
          comments: platform.comments_count,
          shares: platform.views_count, // Não há shares, usar views_count como exemplo
        },
      },
    })) || [];

  // Influencers table
  const influencers =
    influencersData?.data?.map((item: InfluencerItem) => ({
      name: item.influencer_name,
      platform: SOCIAL_NETWORK_CONFIG[item.social_network_type]?.key || 'desconhecido',
      platformType: item.social_network_type,
      verified: false, // Não há campo de verificado
      views: item.views_count,
      likes: item.likes_count,
      comments: item.comments_count,
      womenPercent: `${item.genders.female}%`,
      menPercent: `${item.genders.male}%`,
      photo: item.influencer_photo,
      postLink: item.post_link,
    })) || [];

  return (
    <>
      <div className="min-h-screen bg-neutral-950 text-white p-6">
        {/* Header */}
        <Navbar />

        <CampaignHeader
          title={title}
          profilesCount={profilesCount}
          startDate={campaignData?.start_date}
          endDate={campaignData?.end_date}
        />

        {/* General Stats */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold mb-4">Geral</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="bg-white dark:bg-neutral-900 text-black dark:text-white">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
                  <Eye className="w-4 h-4" />
                  <span>Visualizações</span>
                </div>
                <div className="text-2xl font-medium">{totalViews}</div>
              </CardContent>
            </Card>
            <Card className="bg-white dark:bg-neutral-900 text-black dark:text-white">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
                  <Heart className="w-4 h-4" />
                  <span>Curtidas</span>
                </div>
                <div className="text-2xl font-medium">{totalLikes}</div>
              </CardContent>
            </Card>
            <Card className="bg-white dark:bg-neutral-900 text-black dark:text-white">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
                  <MessageCircle className="w-4 h-4" />
                  <span>Comentários</span>
                </div>
                <div className="text-2xl font-medium">{totalComments}</div>
              </CardContent>
            </Card>
            <Card className="bg-white dark:bg-neutral-900 text-black dark:text-white">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                  <Venus className="w-4 h-4 text-pink-400" />
                  <Mars className="w-4 h-4 text-blue-400" />
                  <span>Gênero</span>
                </div>
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-r from-pink-400 to-blue-400 flex items-center justify-center relative">
                    <div className="w-12 h-12 bg-white rounded-full"></div>
                    <div
                      className="absolute inset-0 rounded-full"
                      style={{
                        background: `conic-gradient(#ec4899 0deg ${69.5 * 3.6}deg, #3b82f6 ${69.5 * 3.6}deg 360deg)`,
                      }}
                    ></div>
                  </div>
                  <div className="text-xs">
                    <div className="flex items-center gap-1 mb-1">
                      <div className="w-2 h-2 bg-pink-400 rounded-full"></div>
                      <span>{totalGenders.female}% mulheres</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                      <span>{totalGenders.male}% homens</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Social Media Breakdown */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold mb-4">Por Rede Social</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {socialPlatforms.map((platform, index) => (
              <Card
                key={index}
                className="bg-white dark:bg-neutral-900 text-black dark:text-white"
              >
                <CardContent className="p-4">
                  <div
                    className={`w-8 h-8 ${platform.bgColor} rounded-lg flex items-center justify-center text-white mb-3`}
                  >
                    {platform.icon}
                  </div>
                  <div className="mb-3">
                    <div className="flex items-center gap-1 mb-1 text-sm">
                      <Venus className="w-3 h-3 text-pink-400" />
                      <span>{platform.stats.women}</span>
                    </div>
                    <div className="flex items-center gap-1 text-sm">
                      <Mars className="w-3 h-3 text-blue-400" />
                      <span>{platform.stats.men}</span>
                    </div>
                  </div>
                  <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                    <span>
                      <Heart className="inline w-4 h-4" />{' '}
                      {platform.stats.engagement.likes}
                    </span>
                    <span>
                      <MessageCircle className="inline w-4 h-4" />{' '}
                      {platform.stats.engagement.comments}
                    </span>
                    <span>
                      <Eye className="inline w-4 h-4" />{' '}
                      {platform.stats.engagement.shares}
                    </span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Influencer Performance Table */}
        <div className="text-white">
          <h2 className="text-lg font-semibold mb-4">Por Perfil</h2>

          <div className="bg-white dark:bg-neutral-900 rounded-lg p-4">
            {/* Search and Filters */}
            <div className="flex flex-col md:flex-row gap-4 mb-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Buscar influencer"
                  className="pl-10 xl:w-1/3 lg:1/3 bg-neutral-100 rounded-xl dark:bg-neutral-900 text-black dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 border-neutral-300 dark:border-neutral-600"
                  value={search}
                  onChange={handleSearchChange}
                />
              </div>
              <div className="flex xl:w-2/4 gap-5">
                <Select
                  value={selectedNetwork === null ? "null" : selectedNetwork.toString()}
                  onValueChange={(value) => setSelectedNetwork(value === "null" ? null : parseInt(value))}
                >
                  <SelectTrigger className="text-black dark:text-white bg-neutral-100 rounded-xl dark:bg-neutral-900 border-neutral-300 dark:border-neutral-600">
                    <SelectValue placeholder="Selecionar rede social" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="null">Todas as redes</SelectItem>
                    <SelectItem value={SOCIAL_NETWORK_TYPES.INSTAGRAM.toString()}>Instagram</SelectItem>
                    <SelectItem value={SOCIAL_NETWORK_TYPES.FACEBOOK_PAGE.toString()}>Facebook Page</SelectItem>
                    <SelectItem value={SOCIAL_NETWORK_TYPES.FACEBOOK_GROUP.toString()}>Facebook Group</SelectItem>
                    <SelectItem value={SOCIAL_NETWORK_TYPES.TIKTOK.toString()}>TikTok</SelectItem>
                    <SelectItem value={SOCIAL_NETWORK_TYPES.YOUTUBE.toString()}>YouTube</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={orderBy} onValueChange={setOrderBy}>
                  <SelectTrigger className="text-black dark:text-white bg-neutral-100 rounded-xl dark:bg-neutral-900 border-neutral-300 dark:border-neutral-600">
                    <SelectValue placeholder="Ordenar por" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={ORDER_BY_OPTIONS.VIEWS}>Visualizações</SelectItem>
                    <SelectItem value={ORDER_BY_OPTIONS.LIKES}>Curtidas</SelectItem>
                    <SelectItem value={ORDER_BY_OPTIONS.COMMENTS}>Comentários</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Table */}
            <Card className="bg-transparent text-black dark:text-white border-none">
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <table className="w-full border-none">
                    <thead>
                      <tr className="text-left">
                        <th className="p-4 font-medium text-gray-600 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700">
                          INFLUENCER
                        </th>
                        <th className="p-4 font-medium text-gray-600 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700">
                          REDE SOCIAL
                        </th>
                        <th className="p-4 font-medium text-gray-600 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700">
                          LINK DA PUBLICAÇÃO
                        </th>
                        <th className="p-4 font-medium text-gray-600 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700">
                          VISUALIZAÇÕES
                        </th>
                        <th className="p-4 font-medium text-gray-600 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700">
                          CURTIDAS
                        </th>
                        <th className="p-4 font-medium text-gray-600 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700">
                          COMENTÁRIOS
                        </th>
                        <th className="p-4 font-medium text-gray-600 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700">
                          GÊNERO
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {influencers.map((influencer, index) => (
                        <tr
                          key={index}
                          className="hover:bg-gray-50 dark:hover:bg-neutral-800"
                        >
                          <td className="p-4 border-t border-gray-100 dark:border-gray-700">
                            <div className="flex items-center gap-2">
                              {influencer.photo ? (
                                <img
                                  src={influencer.photo}
                                  alt={influencer.name}
                                  className="w-8 h-8 rounded-full object-cover bg-gray-200 dark:bg-gray-600"
                                />
                              ) : (
                                <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
                              )}
                              <span className="font-medium">
                                {influencer.name}
                              </span>
                            </div>
                          </td>
                          <td className="p-4 border-t border-gray-100 dark:border-gray-700">
                            <div
                              className={`w-6 h-6 ${getPlatformConfig(influencer.platform)?.bgColor || 'bg-gray-300 dark:bg-gray-600'} rounded flex items-center justify-center text-white text-xs`}
                            >
                              {getPlatformConfig(influencer.platform)?.icon ||
                                influencer.platform}
                            </div>
                          </td>
                          <td className="p-4 border-t border-gray-100 dark:border-gray-700">
                            <Button
                              variant="link"
                              className="p-0 h-auto text-blue-600 dark:text-blue-400"
                            >
                              Ver post
                            </Button>
                          </td>
                          <td className="p-4 font-medium border-t border-gray-100 dark:border-gray-700">
                            {influencer.views}
                          </td>
                          <td className="p-4 font-medium border-t border-gray-100 dark:border-gray-700">
                            {influencer.likes}
                          </td>
                          <td className="p-4 font-medium border-t border-gray-100 dark:border-gray-700">
                            {influencer.comments}
                          </td>
                          <td className="p-4 border-t border-gray-100 dark:border-gray-700">
                            <div className="flex items-center gap-2 text-xs">
                              <div className="flex items-center gap-1">
                                <div className="w-6 h-6  bg-pink-400 rounded-xs "></div>
                                <span className="">
                                  {influencer.womenPercent}
                                </span>
                              </div>
                              <div className="flex items-center gap-1">
                                <div className="w-6 h-6 bg-sky-400 rounded-xs"></div>
                                <span className="">
                                  {influencer.menPercent}
                                </span>
                              </div>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>
          {/* Pagination */}
          <ProfilesPagination
            currentPage={influencersData?.current_page || 1}
            totalItems={influencersData?.total || 0}
            itemsPerPage={itemsPerPage}
            onPageChange={handlePageChange}
            from={influencersData?.from}
            to={influencersData?.to}
          />
        </div>
      </div>
    </>
  );
}











