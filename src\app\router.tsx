// src/app/router.tsx
import {
  createRootRoute,
  createRoute,
  createRouter,
} from '@tanstack/react-router';
import App from '../App';
import Home from '../pages/campaign-report/CampaignReport';

// Root Route (ponto de entrada para todas as rotas)
export const rootRoute = createRootRoute({
  component: App,
});

// Subrotas
const homeRoute = createRoute({
  path: '/',
  getParentRoute: () => rootRoute,
  component: Home,
});

// Roteador
export const router = createRouter({
  routeTree: rootRoute.addChildren([homeRoute]),
});


