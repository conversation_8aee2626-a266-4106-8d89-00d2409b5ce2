import React from 'react';
import {
  Instagram,
  Youtube,
  Music,
  Facebook,
} from 'lucide-react';

// Constantes para os tipos de rede social
export const SOCIAL_NETWORK_TYPES = {
  INSTAGRAM: 1,
  FACEBOOK_PAGE: 2,
  FACEBOOK_GROUP: 3,
  TIKTOK: 4,
  YOUTUBE: 5,
  ALL: null
} as const;

// Constantes de ordenação
export const ORDER_BY_OPTIONS = {
  VIEWS: 'views',
  LIKES: 'likes',
  COMMENTS: 'comments',
} as const;

// Mapeamento unificado dos tipos de rede social
export const SOCIAL_NETWORK_CONFIG: {
  [key: number]: {
    name: string;
    icon: React.ReactNode;
    bgColor: string;
    key: string;
  };
} = {
  [SOCIAL_NETWORK_TYPES.INSTAGRAM]: {
    name: 'Instagram',
    icon: <Instagram className="w-5 h-5" />,
    bgColor: 'bg-gradient-to-br from-purple-500 via-pink-500 to-orange-400',
    key: 'instagram',
  },
  [SOCIAL_NETWORK_TYPES.FACEBOOK_PAGE]: {
    name: 'Facebook Page',
    icon: <Facebook className="w-5 h-5" />,
    bgColor: 'bg-blue-600',
    key: 'facebook',
  },
  [SOCIAL_NETWORK_TYPES.FACEBOOK_GROUP]: {
    name: 'Facebook Group',
    icon: <Facebook className="w-5 h-5" />,
    bgColor: 'bg-blue-800',
    key: 'facebook-group',
  },
  [SOCIAL_NETWORK_TYPES.TIKTOK]: {
    name: 'TikTok',
    icon: <Music className="w-5 h-5" />,
    bgColor: 'bg-black',
    key: 'tiktok',
  },
  [SOCIAL_NETWORK_TYPES.YOUTUBE]: {
    name: 'YouTube',
    icon: <Youtube className="w-5 h-5" />,
    bgColor: 'bg-red-600',
    key: 'youtube',
  },
};

// Helper function to get platform config by key
export const getPlatformConfig = (platformKey: string) => {
  return Object.values(SOCIAL_NETWORK_CONFIG).find(config => config.key === platformKey);
};

// Helper function to get platform config by type
export const getPlatformConfigByType = (platformType: number) => {
  return SOCIAL_NETWORK_CONFIG[platformType];
};
