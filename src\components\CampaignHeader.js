import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Card, CardContent } from '@/components/ui/card';
import { Calendar, Users } from 'lucide-react';
import { formatDate } from '@/lib/utils';
const CampaignHeader = ({ title = 'Dia dos Namorados (O Boticário)', subtitle = 'RELATÓRIO CAMPANHA', period, startDate, endDate, profilesCount = 23, }) => {
    // Format the period using the dates if available
    const formattedPeriod = startDate && endDate
        ? `${formatDate(startDate)} até ${formatDate(endDate)}`
        : period || '12/05/2025 até 12/06/2025';
    return (_jsxs("div", { className: "mb-8", children: [_jsxs("div", { className: "mb-4", children: [_jsx("div", { className: "text-sm font-normal text-neutral-400 ml-2 mb-2", children: subtitle }), _jsx("h1", { className: "text-2xl font-normal text-white", children: title })] }), _jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4 mb-8", children: [_jsx(Card, { className: "bg-white dark:bg-neutral-900 text-black dark:text-white", children: _jsxs(CardContent, { className: "p-4", children: [_jsxs("div", { className: "text-sm  text-gray-600 dark:text-gray-400 mb-1 flex items-center gap-1", children: [_jsx(Calendar, { className: "w-4 h-4" }), "Per\u00EDodo da dura\u00E7\u00E3o da campanha"] }), _jsx("h3", { className: "font-medium ", children: formattedPeriod })] }) }), _jsx(Card, { className: "bg-white dark:bg-neutral-900 text-black dark:text-white", children: _jsxs(CardContent, { className: "p-4", children: [_jsxs("div", { className: "text-sm text-gray-600 dark:text-gray-400 mb-1 flex items-center gap-1", children: [_jsx(Users, { className: "w-4 h-4" }), "Quantidade de perfis participantes"] }), _jsx("h3", { className: "font-medium", children: profilesCount })] }) })] })] }));
};
export default CampaignHeader;
