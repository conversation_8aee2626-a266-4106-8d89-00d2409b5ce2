import { CampaignData, InfluencersData } from '@/types/api';
export interface UseCampaignAnalyticsReturn {
    campaignData: CampaignData | undefined;
    influencersData: InfluencersData | undefined;
    socialPlatforms: Array<{
        name: string;
        icon: React.ReactNode;
        bgColor: string;
        stats: {
            women: string;
            men: string;
            engagement: {
                likes: number;
                comments: number;
                shares: number;
            };
        };
    }>;
    influencers: Array<{
        name: string;
        platform: string;
        platformType: number;
        verified: boolean;
        views: number;
        likes: number;
        comments: number;
        womenPercent: string;
        menPercent: string;
        photo: string;
        postLink: string;
    }>;
    totalViews: number;
    totalLikes: number;
    totalComments: number;
    totalGenders: {
        female: number;
        male: number;
    };
    profilesCount: number | string;
    title: string;
    currentPage: number;
    itemsPerPage: number;
    search: string;
    debouncedSearch: string;
    selectedNetwork: number | null;
    orderBy: string;
    setCurrentPage: (page: number) => void;
    setSearch: (search: string) => void;
    setSelectedNetwork: (network: number | null) => void;
    setOrderBy: (orderBy: string) => void;
    handleSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handlePageChange: (page: number) => void;
}
export declare function useCampaignAnalytics(): UseCampaignAnalyticsReturn;
