import { useState, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import debounce from 'lodash/debounce';
import { fetchCampaignData, fetchInfluencersData } from '@/APIs/analytics';
import { SOCIAL_NETWORK_TYPES, ORDER_BY_OPTIONS } from '@/constants/socialNetworks';
export function useCampaignAnalytics() {
    // Estados para paginação e filtros
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(10);
    const [search, setSearch] = useState('');
    const [debouncedSearch, setDebouncedSearch] = useState('');
    const [selectedNetwork, setSelectedNetwork] = useState(SOCIAL_NETWORK_TYPES.ALL);
    const [orderBy, setOrderBy] = useState(ORDER_BY_OPTIONS.VIEWS);
    // Fetch dos dados gerais da campanha
    const { data: campaignData } = useQuery({
        queryKey: ['campaign-report'],
        queryFn: fetchCampaignData,
    });
    // Fetch dos dados por perfil (influencers) com parâmetro de busca
    const { data: influencersData } = useQuery({
        queryKey: ['campaign-report-influencers', currentPage, itemsPerPage, debouncedSearch, selectedNetwork, orderBy],
        queryFn: () => fetchInfluencersData(currentPage, itemsPerPage, debouncedSearch, selectedNetwork, orderBy),
    });
    // Debounce function for search
    // eslint-disable-next-line react-hooks/exhaustive-deps
    const debouncedSetSearch = useCallback(debounce((value) => {
        setDebouncedSearch(value);
    }, 1000), []);
    // Handle search input change
    const handleSearchChange = (e) => {
        const value = e.target.value;
        setSearch(value);
        debouncedSetSearch(value);
    };
    // Função para lidar com mudança de página
    const handlePageChange = (page) => {
        setCurrentPage(page);
    };
    // Computed values
    const totalViews = campaignData?.views_count || 0;
    const totalLikes = campaignData?.likes_count || 0;
    const totalComments = campaignData?.comments_count || 0;
    const totalGenders = campaignData?.genders || { female: 0, male: 0 };
    const profilesCount = typeof influencersData?.total === 'number' ? influencersData.total : 'não informado';
    const title = campaignData?.name || 'TITULO DE CAMPANHA NÃO INFORMADO';
    return {
        // Data
        campaignData,
        influencersData,
        socialPlatforms: [], // Will be computed in component with SOCIAL_NETWORK_CONFIG
        influencers: [], // Will be computed in component with SOCIAL_NETWORK_CONFIG
        // Computed values
        totalViews,
        totalLikes,
        totalComments,
        totalGenders,
        profilesCount,
        title,
        // State
        currentPage,
        itemsPerPage,
        search,
        debouncedSearch,
        selectedNetwork,
        orderBy,
        // Actions
        setCurrentPage,
        setSearch,
        setSelectedNetwork,
        setOrderBy,
        handleSearchChange,
        handlePageChange,
    };
}
