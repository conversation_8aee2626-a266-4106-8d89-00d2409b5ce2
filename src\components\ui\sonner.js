"use client";
import { jsx as _jsx } from "react/jsx-runtime";
import { useTheme } from "next-themes";
import { Toaster as Sonner } from "sonner";
const Toaster = ({ ...props }) => {
    const { theme = "system" } = useTheme();
    return (_jsx(Sonner, { theme: theme, className: "toaster group", toastOptions: {
            classNames: {
                toast: "group toast group-[.toaster]:bg-white group-[.toaster]:dark:bg-neutral-900 group-[.toaster]:text-black group-[.toaster]:dark:text-white group-[.toaster]:border-neutral-200 group-[.toaster]:dark:border-neutral-800 group-[.toaster]:shadow-lg",
                description: "group-[.toast]:text-gray-600 group-[.toast]:dark:text-gray-400",
                actionButton: "group-[.toast]:bg-neutral-900 group-[.toast]:dark:bg-white group-[.toast]:text-white group-[.toast]:dark:text-black",
                cancelButton: "group-[.toast]:bg-neutral-100 group-[.toast]:dark:bg-neutral-900 group-[.toast]:text-neutral-600 group-[.toast]:dark:text-neutral-400",
            },
        }, ...props }));
};
export { Toaster };
