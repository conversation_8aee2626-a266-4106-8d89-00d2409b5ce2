export interface GenderStats {
    female: number;
    male: number;
}
export interface SocialNetwork {
    type: number;
    name: string;
    views_count: number;
    likes_count: number;
    comments_count: number;
    genders: GenderStats;
}
export interface CampaignData {
    name: string;
    social_networks_count: number;
    views_count: number;
    likes_count: number;
    comments_count: number;
    genders: GenderStats;
    by_social_network: SocialNetwork[];
    start_date?: string;
    end_date?: string;
}
export interface InfluencerItem {
    social_network_type: number;
    influencer_name: string;
    influencer_photo: string;
    post_link: string;
    views_count: number;
    likes_count: number;
    comments_count: number;
    genders: GenderStats;
}
export interface InfluencersData {
    current_page: number;
    data: InfluencerItem[];
    total: number;
    per_page?: number;
    last_page?: number;
    from?: number;
    to?: number;
}
